package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;


import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_HOST;
import static java.util.Arrays.asList;

/**
 * ZStack主机基础数据收集实现
 * 采用线程安全的ZStackClientWrapper进行API调用
 *
 * {{RIPER-5:
 *   Action: "Modified"
 *   Task_ID: "ZStack主机数据收集标准化改进"
 *   Timestamp: "2025-01-30T12:30:00Z"
 *   Authoring_Role: "LD"
 *   Principle_Applied: "SOLID-S (单一职责原则) + 线程安全设计"
 *   Quality_Check: "API调用标准化，错误处理完善，线程安全验证"
 * }}
 *
 * <AUTHOR>
 **/
@Slf4j
public class ZsTackBasicHostDataImpl extends AbstractBasicData {

    protected long startTime;

    private static final BigDecimal ZERO = new BigDecimal(0);
    private static final String KVM = "KVM";
    private static final String HOST_UUID_PREFIX = "HostUuid=";

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }

            taskExecutor.execute(() -> {
                try {
                    // {{RIPER-5:
                    //   Action: "Modified"
                    //   Task_ID: "ZStack异步线程安全改进"
                    //   Timestamp: "2025-01-30T12:30:00Z"
                    //   Authoring_Role: "LD"
                    //   Principle_Applied: "线程安全 + 异常处理"
                    //   Quality_Check: "异步调用安全性验证，错误处理完善"
                    // }}
                    List<HostData> data = collectData(platform);
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(data).metricsName(BASIC_HOST.code()).build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("平台 {} 主机数据收集完成，耗时 {} 秒", platform.getPlatformName(), endTimeFormatted);
                } catch (Exception e) {
                    log.error("平台 {} 主机数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                    // 如果是认证相关错误，可以考虑重试或标记平台状态
                    handleCollectionError(platform, e);
                }
            });
        }
    }

    private List<HostData> collectData(Platform platform) {
        List<HostData> hostDataList = Lists.newArrayList();

        // 使用线程安全的ZStack API调用查询区域列表
        QueryZoneAction.Result zoneRes = ZStackClientWrapper.queryZonesAsync(platform);
        List<?> regionList = zoneRes.value.inventories;

        // 使用线程安全的主机查询
        QueryHostAction.Result hostResult = ZStackClientWrapper.queryHostsAsync(platform);
        if (hostResult.value != null) {
            processHostList(hostResult.value.inventories, platform, regionList, hostDataList);
        }

        return hostDataList;
    }


    private void processHostList(List<?> hostList, Platform platform, List<?> regionList, List<HostData> hostDataList) {
        for (Object host : hostList) {
            JsonObject jsonObject = GsonUtil.GSON.toJsonTree(host).getAsJsonObject();
            JsonElement hypervisorType = jsonObject.get("hypervisorType");
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            if (hypervisorType != null && !hypervisorType.isJsonNull() &&
                    KVM.equals(hypervisorType.getAsString()) && uuid != null) {
                processHostData(jsonObject, platform, regionList, hostDataList);
            }
        }
    }

    private void processHostData(JsonObject jsonObject, Platform platform, List<?> regionList, List<HostData> hostDataList) {
        try {
            HostData hostData = new HostData();
            AtomicReference<String> clusterName = new AtomicReference<>();
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            String name = getStringFromJson(jsonObject, "name", "-");
            String state = getStringFromJson(jsonObject, "state", "-");
            String ip = getStringFromJson(jsonObject, "managementIp", "-");
            String status = getStringFromJson(jsonObject, "status", "-");
            String clusterUuid = getStringFromJson(jsonObject, "clusterUuid", "");
            String zoneUuid = getStringFromJson(jsonObject, "zoneUuid", "");
            hostData.setManager("zstack");
            hostData.setAvailableManager("-");
            // 设置区域名称
            for (Object item : regionList) {
                if (item instanceof ZoneInventory) {
                    ZoneInventory zone = (ZoneInventory) item;
                    if (zone.getUuid().equals(zoneUuid)) {
                        hostData.setAvailableManager(zone.getName());
                        break;
                    }
                }
            }
            // CPU相关数据
            Long totalCpuCapacity = getLongFromJson(jsonObject, "totalCpuCapacity");
            Long availableCpuCapacity = getLongFromJson(jsonObject, "availableCpuCapacity");
            Integer cpuSockets = getIntFromJson(jsonObject, "cpuSockets");
            String architecture = getStringFromJson(jsonObject, "architecture", "-");
            Integer cpuNum = getIntFromJson(jsonObject, "cpuNum");
            BigDecimal cpuCommitRate = new BigDecimal(totalCpuCapacity - availableCpuCapacity);
            // 内存相关数据
            Long totalMemoryCapacity = getLongFromJson(jsonObject, "totalMemoryCapacity");
            Long availableMemoryCapacity = getLongFromJson(jsonObject, "availableMemoryCapacity");
            BigDecimal memCommitRate = new BigDecimal(totalMemoryCapacity - availableMemoryCapacity);
            // 创建日期
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy, h:mm:ss a", Locale.US);
            Date createDate;
            try {
                createDate = sdf.parse("Aug 19, 2020, 5:25:01 PM");
            } catch (ParseException e) {
                createDate = new Date();
                log.error("解析日期失败", e);
            }
            // 使用线程安全的集群查询
            QueryClusterAction.Result clusterResult = ZStackClientWrapper.queryClustersWithConditionsAsync(platform, asList("hypervisorType=KVM"));
            clusterResult.value.inventories.forEach(cluster -> {
                JsonObject clusterObj = GsonUtil.GSON.toJsonTree(cluster).getAsJsonObject();
                if (clusterUuid.equals(clusterObj.get("uuid").getAsString())) {
                    clusterName.set(clusterObj.get("name").getAsString());
                }
            });

            // 使用线程安全的指标数据获取方法
            BigDecimal memory_used = ZStackClientWrapper.getHostMetricDataAsync(platform, "MemoryUsedInPercent", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "MemoryUsedInPercent", uuid)) : ZERO;
            BigDecimal cpu_useds = ZStackClientWrapper.getHostMetricDataAsync(platform, "CPUAverageUsedUtilization", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "CPUAverageUsedUtilization", uuid)) : ZERO;
            BigDecimal diskUsed = ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllUsedCapacityInPercent", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllUsedCapacityInPercent", uuid)) : ZERO;
            BigDecimal diskFreeBytes = ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllFreeCapacityInBytes", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllFreeCapacityInBytes", uuid)) : ZERO;
            BigDecimal diskUsedBytes = ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllUsedCapacityInBytes", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllUsedCapacityInBytes", uuid)) : ZERO;
            BigDecimal bandwidth_downstream = ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllOutBytes", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllOutBytes", uuid)) : ZERO;
            BigDecimal bandwidth_upstream = ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllInBytes", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllInBytes", uuid)) : ZERO;
            BigDecimal packet_rate = ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllInPackets", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllInPackets", uuid)) : ZERO;

            // 获取CPU超分配置 (使用线程安全的资源配置查询)
            String cpuOverProvisioningValue = getResourceConfigSafe(platform, "host", "cpu.overProvisioning.ratio", clusterUuid);
            BigDecimal cpuOversold = new BigDecimal(cpuOverProvisioningValue != null ? cpuOverProvisioningValue : "1.0");
            hostData.setCpuOverPercent(cpuOversold);

            // 获取内存超售配置 (使用线程安全的资源配置查询)
            String memoryOverProvisioningValue = getResourceConfigSafe(platform, "mevoco", "overProvisioning.memory", clusterUuid);
            BigDecimal memoryOversold = new BigDecimal(memoryOverProvisioningValue != null ? memoryOverProvisioningValue : "1.0");
            hostData.setMemoryOverPercent(memoryOversold);

            // 获取预留内存配置
            String reservedMemoryValue = getResourceConfig("kvm", "reservedMemory", clusterUuid, token, platform);
            if (reservedMemoryValue != null) {
                BigDecimal reserved = new BigDecimal(reservedMemoryValue.replace("G", ""))
                        .multiply(new BigDecimal(1024 * 1024 * 1024));
                hostData.setReservedMemory(reserved);
            }

            // 获取主机网络接口
            QueryHostNetworkInterfaceAction macAction = new QueryHostNetworkInterfaceAction();
            macAction.conditions = asList("hostUuid=" + uuid);
            if (platform.getAkType() == 0) {
                macAction.sessionId = token;
            } else {
                macAction.accessKeyId = platform.getUsername();
                macAction.accessKeySecret = platform.getPassword();
            }
            macAction.call();


            String retainMemValue = getResourceConfigSafe(platform, "kvm", "reservedMemory", uuid);
            Long retainMem = 0L;
            if (retainMemValue != null) {
                retainMem = StringUtil.convertToKB(retainMemValue);
            } else {
                // 如果单个没查到查全部 (使用线程安全的全局配置查询)
                QueryGlobalConfigAction.Result globalConfigResult = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                    QueryGlobalConfigAction action = new QueryGlobalConfigAction();
                    ZStackClientWrapper.setAuthentication(action, platform);
                    return action.call();
                });

                if (globalConfigResult != null && globalConfigResult.value != null && CollUtil.isNotEmpty(globalConfigResult.value.inventories)) {
                    for (Object list1 : globalConfigResult.value.inventories) {
                        JsonObject globalConfig1 = new Gson().toJsonTree(list1).getAsJsonObject();
                        if ("reservedMemory".equals(globalConfig1.get("name").getAsString())) {
                            retainMem = StringUtil.convertToKB(globalConfig1.get("value").getAsString());
                        }
                    }
                }
            }
            BigDecimal availableMem = NumberUtil.sub(Convert.toBigDecimal(availableMemoryCapacity), retainMem);
            if (availableMem.signum() < 0) {
                availableMem = new BigDecimal(0);
            }

            // 使用线程安全的用户标签查询
            QueryUserTagAction.Result restag = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                QueryUserTagAction action = new QueryUserTagAction();
                action.conditions = asList("resourceUuid=" + uuid, "resourceType=HostVO");
                ZStackClientWrapper.setAuthentication(action, platform);
                return action.call();
            });

            List<String> tagValues = new ArrayList<>();
            for (Object inventory : restag.value.inventories) {
                JsonObject tagDO = GsonUtil.GSON.toJsonTree(inventory).getAsJsonObject().getAsJsonObject("tagPattern");
                String tag = getStringFromJson(tagDO, "name", "");
                String taguuid = getStringFromJson(tagDO, "uuid", "");
                if (StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(taguuid)) {
                    tagValues.add(tag + "&" + taguuid);
                }
            }
            String resultTag = tagValues.isEmpty() ? "" : (tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));

            hostData.setTag(resultTag);
            hostData.setModel("-");
            hostData.setBrandName("Zstack");
            hostData.setSerialNumber("-");
            //cpu类型（描述）
            hostData.setCpuType("-");
            hostData.setIpmi("-");
            hostData.setManufacturer("-");
            hostData.setUuid(uuid);
            hostData.setName(name);
            hostData.setState(state);
            hostData.setIp(ip);
            hostData.setStatus(status);
            hostData.setClusterUuid(clusterUuid);
            hostData.setClusterName(clusterName.toString());
            hostData.setTotalCpuCapacity(totalCpuCapacity);
            hostData.setTotalVirtualMemory(Convert.toBigDecimal(totalMemoryCapacity).multiply(memoryOversold));
            hostData.setAvailableCpuCapacity(availableCpuCapacity);
            hostData.setCpuSockets(cpuSockets);
            hostData.setArchitecture(architecture);
            hostData.setCpuNum(cpuNum);
            hostData.setCpuCommitRate(cpuCommitRate);
            hostData.setMemoryCommitRate(memCommitRate);
            hostData.setTotalMemoryCapacity(totalMemoryCapacity);
            hostData.setAvailableMemoryCapacity(Convert.toLong(availableMem));
            hostData.setBandwidthUpstream(bandwidth_upstream);
            hostData.setBandwidthDownstream(bandwidth_downstream);
            hostData.setMemoryUsed(memory_used);
            hostData.setPacketRate(packet_rate);
            hostData.setCpuUsed(cpu_useds);
            hostData.setDiskUsed(diskUsed);
            hostData.setDiskUsedBytes(diskUsedBytes);
            hostData.setDiskFreeBytes(diskFreeBytes);
            hostData.setTotalDiskCapacity(diskUsedBytes.add(diskFreeBytes));
            hostData.setTenantId(0L);
            hostData.setRegionId(platform.getRegionId());
            hostData.setPlatformId(platform.getPlatformId());
            hostData.setPlatformName(platform.getPlatformName());
            hostData.setDeleted(0);
            hostData.setTypeName("zstack");
            hostData.setCreateTime(createDate);
            hostData.setIsMaintain(0);
            hostDataS.add(hostData);
        } catch (Exception e) {
            log.error("host data error:{}", ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * 从JsonObject中安全获取BigDecimal值
     *
     * @param jsonObject   JSON对象
     * @param key          键
     * @param defaultValue 默认值
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalFromJson(JsonObject jsonObject, String key, BigDecimal defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsBigDecimal();
        }
        return defaultValue;
    }

    /**
     * 从指标查询结果中提取数值
     *
     * @param result 指标查询结果
     * @return 指标值
     */
    private BigDecimal getMetricValue(GetMetricDataAction.Result result) {
        try {
            if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                return dataObj.get("value").getAsBigDecimal();
            }
        } catch (Exception e) {
            log.error("解析指标数据失败: {}", e.getMessage());
        }
        return ZERO;
    }

    /**
     * 线程安全的资源配置获取方法
     *
     * @param platform     平台信息
     * @param category     类别
     * @param name         名称
     * @param resourceUuid 资源UUID
     * @return 配置值
     */
    private String getResourceConfigSafe(Platform platform, String category, String name, String resourceUuid) {
        try {
            GetResourceConfigAction.Result result = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                GetResourceConfigAction action = new GetResourceConfigAction();
                action.category = category;
                action.name = name;
                action.resourceUuid = resourceUuid;
                ZStackClientWrapper.setAuthentication(action, platform);
                return action.call();
            });

            if (result != null && result.value != null) {
                return result.value.value;
            }
        } catch (Exception e) {
            log.error("获取资源配置失败: category={}, name={}, resourceUuid={}", category, name, resourceUuid, e);
        }
        return null;
    }

    /**
     * 处理数据收集错误
     */
    private void handleCollectionError(Platform platform, Exception e) {
        String errorMessage = e.getMessage();

        if (errorMessage != null && (errorMessage.contains("session expired") ||
            errorMessage.contains("does not existed or disabled") ||
            errorMessage.contains("Token为空或无效"))) {

            log.warn("平台 {} 认证失败，可能需要重新获取认证信息: {}", platform.getPlatformName(), errorMessage);
            // 这里可以添加重新获取认证信息的逻辑
            // 或者标记平台为需要重新认证状态

        } else if (errorMessage != null && errorMessage.contains("网络不可达")) {
            log.warn("平台 {} 网络连接失败: {}", platform.getPlatformName(), errorMessage);
            // 可以标记平台为离线状态

        } else {
            log.error("平台 {} 数据收集发生未知错误: {}", platform.getPlatformName(), errorMessage);
        }
    }



    /**
     * 从JsonObject中安全获取长整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 长整型值
     */
    private Long getLongFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsLong();
        }
        return 0L;
    }

    /**
     * 从JsonObject中安全获取整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 整型值
     */
    private Integer getIntFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsInt();
        }
        return 0;
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_HOST.code();
    }
}
