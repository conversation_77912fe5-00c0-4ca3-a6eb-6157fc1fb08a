package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_ZS_TACK_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static java.util.Arrays.asList;


@Slf4j
public class ZsTackCpuImpl extends AbstractMetrics {


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {

        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) o;
                if (platform.getState() == 1) {
                    log.warn("平台 {} 处于离线状态，跳过CPU指标收集", platform.getPlatformName());
                    return;
                }

                try {
                    // {{RIPER-5:
                    //   Action: "Modified"
                    //   Task_ID: "ZStack异步线程安全改进"
                    //   Timestamp: "2025-01-30T10:00:00Z"
                    //   Authoring_Role: "LD"
                    //   Principle_Applied: "线程安全 + 异常处理"
                    //   Quality_Check: "异步调用安全性验证，错误处理完善"
                    // }}
                    List<MetricData> metricDataList = new ArrayList<>();

                    // 获取所有VM和Host的UUID
                    List<MonitorInfo> vmUuids = platform.getZsTackPlatform().getVmUuids();
                    List<MonitorInfo> hostUuids = platform.getZsTackPlatform().getHostUuids();

                    if (vmUuids == null || vmUuids.isEmpty()) {
                        log.warn("平台 {} VM UUID为空，跳过VM CPU指标收集", platform.getPlatformName());
                    } else {
                        metricDataList.addAll(vmUUid(vmUuids, platform));
                    }

                    if (hostUuids != null && !hostUuids.isEmpty()) {
                        metricDataList.addAll(hostUUid(hostUuids, platform));
                    }

                    if (!metricDataList.isEmpty()) {
                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.CPU_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        log.info("平台 {} CPU指标收集完成，收集到 {} 条数据", platform.getPlatformName(), metricDataList.size());
                    }

                } catch (Exception e) {
                    log.error("平台 {} CPU指标收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                    handleCollectionError(platform, e);
                }
            });
        }

    }


    public List<MetricData> vmUUid(List<MonitorInfo> vmUuids, Platform platform) {
        return getMetricData(vmUuids, "ZStack/VM", "VMUuid=", platform,"vm");
    }

    public List<MetricData> hostUUid(List<MonitorInfo> hostUuids, Platform platform) {
        return getMetricData(hostUuids, "ZStack/Host", "HostUuid=", platform,"host");
    }

    private List<MetricData> getMetricData(List<MonitorInfo> uuids, String namespace, String labelPrefix, Platform platform, String type) {
        List<MetricData> metricDataList = new ArrayList<>();
        for (MonitorInfo monitorInfo : uuids) {
            try {
                // 使用线程安全的异步方法获取指标数据
                GetMetricDataAction.Result result;
                if ("vm".equals(type)) {
                    result = ZStackClientWrapper.getVmMetricDataAsync(platform, "CPUAverageUsedUtilization", monitorInfo.getUuid());
                } else {
                    result = ZStackClientWrapper.getHostMetricDataAsync(platform, "CPUAverageUsedUtilization", monitorInfo.getUuid());
                }

                if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                    MetricData metricData = new MetricData();
                    metricData.setResourceId(monitorInfo.getUuid());
                    metricData.setResourceName(monitorInfo.getName());
                    metricData.setPlatformId(platform.getPlatformId());
                    metricData.setType(type);

                    List<Long> timestamps = new ArrayList<>();
                    List<Double> values = new ArrayList<>();

                    JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                    timestamps.add(dataObj.get("time").getAsLong());
                    values.add(dataObj.get("value").getAsDouble());

                    metricData.setMetricName(CPU_USED_TASK.code());
                    metricData.setTimestamps(timestamps);
                    metricData.setValues(values);
                    metricDataList.add(metricData);
                }

            } catch (RuntimeException e) {
                log.error("平台 {} {}({}) CPU指标收集失败: {}",
                    platform.getPlatformName(), type, monitorInfo.getUuid(), e.getMessage());
                // 继续处理其他资源，不中断整个流程
            }
        }
        return metricDataList;
    }

    /**
     * 处理数据收集错误
     */
    private void handleCollectionError(Platform platform, Exception e) {
        String errorMessage = e.getMessage();

        if (errorMessage != null && (errorMessage.contains("session expired") ||
            errorMessage.contains("does not existed or disabled") ||
            errorMessage.contains("Token为空或无效"))) {

            log.warn("平台 {} 认证失败，可能需要重新获取认证信息: {}", platform.getPlatformName(), errorMessage);

        } else if (errorMessage != null && errorMessage.contains("网络不可达")) {
            log.warn("平台 {} 网络连接失败: {}", platform.getPlatformName(), errorMessage);

        } else {
            log.error("平台 {} CPU指标收集发生未知错误: {}", platform.getPlatformName(), errorMessage);
        }
    }


    @Override
    public String supportProtocol() {
        return PROTOCOL_ZS_TACK_CPU.code();
    }
}
