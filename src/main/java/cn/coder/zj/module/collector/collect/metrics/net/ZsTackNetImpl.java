package cn.coder.zj.module.collector.collect.metrics.net;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_IN_TASK;
import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_OUT_TASK;
import static cn.coder.zj.module.collector.enums.NetType.PROTOCOL_ZS_TACK_NET;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackNetImpl extends AbstractMetrics {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) o;
                if (platform.getState() == 1) {
                    log.warn("平台 {} 处于离线状态，跳过网络指标收集", platform.getPlatformName());
                    return;
                }

                try {
                    // {{RIPER-5:
                    //   Action: "Modified"
                    //   Task_ID: "ZStack异步线程安全改进"
                    //   Timestamp: "2025-01-30T10:00:00Z"
                    //   Authoring_Role: "LD"
                    //   Principle_Applied: "线程安全 + 异常处理"
                    //   Quality_Check: "异步调用安全性验证，错误处理完善"
                    // }}
                    List<MetricData> metricDataList = new ArrayList<>();

                    // 获取所有VM和Host的UUID
                    List<MonitorInfo> vmUuids = platform.getZsTackPlatform().getVmUuids();
                    List<MonitorInfo> hostUuids = platform.getZsTackPlatform().getHostUuids();

                    if (vmUuids == null || vmUuids.isEmpty()) {
                        log.warn("平台 {} VM UUID为空，跳过VM网络指标收集", platform.getPlatformName());
                    } else {
                        networkOutTask(vmUuids, hostUuids, platform, metricDataList);
                        networkInTask(vmUuids, hostUuids, platform, metricDataList);
                    }

                    if (!metricDataList.isEmpty()) {
                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.NET_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());

                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("平台 {} 网络指标收集完成，收集到 {} 条数据，耗时 {} 秒",
                            platform.getPlatformName(), metricDataList.size(), endTimeFormatted);
                    }

                } catch (Exception e) {
                    log.error("平台 {} 网络指标收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                    handleCollectionError(platform, e);
                }
            });
        }
    }

    private List<MetricData> networkInTask(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, "ZStack/VM", "VMUuid=", platform, NETWORK_IN_TASK.code(), metricDataList);
        getMetricHostData(hostUuids, "ZStack/Host", "HostUuid=", platform, NETWORK_IN_TASK.code(), metricDataList);
        return metricDataList;
    }

    private List<MetricData> networkOutTask(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, "ZStack/VM", "VMUuid=", platform, NETWORK_OUT_TASK.code(), metricDataList);
        getMetricHostData(hostUuids, "ZStack/Host", "HostUuid=", platform, NETWORK_OUT_TASK.code(), metricDataList);
        return metricDataList;
    }

    private List<MetricData> getMetricVmData(List<MonitorInfo> vmUuids, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList) {
        return getMetricData(vmUuids, namespace, labelPrefix, platform, code, metricDataList,"vm");
    }

    private List<MetricData> getMetricHostData(List<MonitorInfo> hostUuids, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList) {
        return getMetricData(hostUuids, namespace, labelPrefix, platform, code, metricDataList,"host");
    }

    private List<MetricData> getMetricData(List<MonitorInfo> uuids, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList, String type) {
        for (MonitorInfo monitorInfo : uuids) {
            try {
                // 使用线程安全的异步方法获取指标数据
                GetMetricDataAction.Result result;
                if ("vm".equals(type)) {
                    result = ZStackClientWrapper.getVmMetricDataAsync(platform, code, monitorInfo.getUuid());
                } else {
                    result = ZStackClientWrapper.getHostMetricDataAsync(platform, code, monitorInfo.getUuid());
                }

                if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                    MetricData metricData = new MetricData();
                    metricData.setResourceId(monitorInfo.getUuid());
                    metricData.setResourceName(monitorInfo.getName());
                    metricData.setPlatformId(platform.getPlatformId());
                    metricData.setType(type);

                    List<Long> timestamps = new ArrayList<>();
                    List<Double> values = new ArrayList<>();

                    JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                    timestamps.add(dataObj.get("time").getAsLong());
                    values.add(dataObj.get("value").getAsDouble());

                    metricData.setMetricName(code);
                    metricData.setTimestamps(timestamps);
                    metricData.setValues(values);
                    metricDataList.add(metricData);
                }

            } catch (RuntimeException e) {
                log.error("平台 {} {}({}) 网络指标({})收集失败: {}",
                    platform.getPlatformName(), type, monitorInfo.getUuid(), code, e.getMessage());
                // 继续处理其他资源，不中断整个流程
            }
        }
        return metricDataList;
    }

    /**
     * 处理数据收集错误
     */
    private void handleCollectionError(Platform platform, Exception e) {
        String errorMessage = e.getMessage();

        if (errorMessage != null && (errorMessage.contains("session expired") ||
            errorMessage.contains("does not existed or disabled") ||
            errorMessage.contains("Token为空或无效"))) {

            log.warn("平台 {} 认证失败，可能需要重新获取认证信息: {}", platform.getPlatformName(), errorMessage);

        } else if (errorMessage != null && errorMessage.contains("网络不可达")) {
            log.warn("平台 {} 网络连接失败: {}", platform.getPlatformName(), errorMessage);

        } else {
            log.error("平台 {} 网络指标收集发生未知错误: {}", platform.getPlatformName(), errorMessage);
        }
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_ZS_TACK_NET.code();
    }
}
